// Enhanced Price Scale Drag Functionality for TradeCraft Charts
document.addEventListener('DOMContentLoaded', function() {

    // Function to enhance price scale interaction
    function enhancePriceScaleDrag() {
        const chartContainer = document.getElementById('btc-candle-chart');
        if (!chartContainer) return;

        // Wait for Plotly to initialize
        setTimeout(() => {
            const plotlyDiv = chartContainer.querySelector('.js-plotly-plot');
            if (!plotlyDiv) return;

            // Add event listeners for enhanced Y-axis interaction
            plotlyDiv.addEventListener('plotly_relayout', function(eventData) {
                // Handle Y-axis range changes
                if (eventData && (eventData['yaxis.range[0]'] !== undefined || eventData['yaxis.range[1]'] !== undefined)) {
                    console.log('Y-axis range updated:', eventData);
                }
            });

            // Enhance cursor feedback on Y-axis area
            const yAxisElements = plotlyDiv.querySelectorAll('.yaxis, .yaxislayer-above');
            yAxisElements.forEach(element => {
                element.style.cursor = 'ns-resize';

                element.addEventListener('mouseenter', function() {
                    this.style.opacity = '0.8';
                });

                element.addEventListener('mouseleave', function() {
                    this.style.opacity = '1';
                });
            });

        }, 1000);
    }

    // Initialize enhancement
    enhancePriceScaleDrag();

    // Re-initialize when page content changes (for SPA navigation)
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'childList') {
                enhancePriceScaleDrag();
            }
        });
    });

    observer.observe(document.body, {
        childList: true,
        subtree: true
    });
});

// Additional utility function for smooth Y-axis transitions
function smoothYAxisTransition(plotlyDiv, newRange) {
    if (!plotlyDiv || !newRange) return;

    Plotly.relayout(plotlyDiv, {
        'yaxis.range': newRange,
        'yaxis.transition': {
            duration: 300,
            easing: 'cubic-in-out'
        }
    });
}