/* Scrollbar styling for Chrome, Safari */
::-webkit-scrollbar {
    height: 6px;  /* Controls horizontal scrollbar height */
    width: 6px;   /* Controls vertical scrollbar width */
}

::-webkit-scrollbar-thumb {
    background-color: #888;  /* Scrollbar color */
    border-radius: 5px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;  /* Track color */
}

::-webkit-scrollbar-button {
    display: none;
}

/* For Firefox */
* {
    scrollbar-width: thin; /* Makes scrollbar thinner */
    scrollbar-color: gray transparent; /* Custom scrollbar */
}

/* Date Picker Modal Styling */
.date-picker-modal .modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.date-picker-modal .modal-header {
    background-color: #f8f9fa;
    border-top-left-radius: 12px;
    border-top-right-radius: 12px;
}

/* Calendar day styling */
#calendar-body-1 .d-inline-block, 
#calendar-body-2 .d-inline-block {
    transition: all 0.2s ease;
}

#calendar-body-1 .d-inline-block:hover, 
#calendar-body-2 .d-inline-block:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    z-index: 1;
    position: relative;
}

/* Highlighted dates styling */
#calendar-body-1 .d-inline-block[style*="backgroundColor: #0056b3"],
#calendar-body-2 .d-inline-block[style*="backgroundColor: #0056b3"] {
    box-shadow: 0 2px 8px rgba(0,86,179,0.4);
}

/* Manual Trade Entry Modal Styles */
.manual-trade-modal .modal-dialog {
    max-width: 50%;
}

.manual-trade-modal .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

.manual-trade-modal .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.manual-trade-modal .order-card {
    border: 1px solid #e3e6f0;
    border-radius: 0.35rem;
    box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
}

.manual-trade-modal .order-card:hover {
    box-shadow: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
    transition: all 0.3s;
}

.manual-trade-modal .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
}

.manual-trade-modal .form-control:focus,
.manual-trade-modal .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* Add Order Modal Styles */
.add-order-modal .modal-dialog {
    max-width: 50%;
}

.add-order-modal .modal-body {
    max-height: 70vh;
    overflow-y: auto;
}

.add-order-modal .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

.add-order-modal .form-control:focus,
.add-order-modal .form-select:focus {
    border-color: #198754;
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}
