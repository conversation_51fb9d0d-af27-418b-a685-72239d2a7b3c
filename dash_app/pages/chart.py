import dash
import plotly.graph_objects as go
from dash import html, dcc, Input, Output
from flask import session

from services import binance as binance_service

dash.register_page(__name__, name="TradeCraft - Chart")

# Layout: Full-screen chart area with an auto-refresh interval
controls_layout = html.Div([
    dcc.Interval(id="btc-candle-refresh", interval=60 * 1000, n_intervals=0),  # refresh every 60s
    dcc.Graph(id="btc-candle-chart", config={
        "displaylogo": False,
        "scrollZoom": True,
        "doubleClick": "reset",
        "modeBarButtonsToRemove": [
            "toggleSpikelines"
        ]
    }, style={
        # "height": "calc(100vh - 20px)",
        "width": "90%",
        "height": "80%",
    })
], style={
    "padding": "0",
    "margin": "0",
    "width": "100%",
})


def layout():
    if "user_id" not in session:
        return dcc.Location(pathname="/login", id="redirect")
    return controls_layout


# Callback to load/update BTCUSDT 1H candlestick chart
@dash.callback(
    Output("btc-candle-chart", "figure"),
    Input("btc-candle-refresh", "n_intervals")
)
def update_btc_candles(_):
    # Fetch 1-hour interval candlesticks for BTC/USDT
    df = binance_service.get_candlestick_dataframe(symbol="BTCUSDT", limit=1000)

    if df is None or df.empty:
        # Return an empty figure with annotation if no data
        fig = go.Figure()
        fig.update_xaxes(fixedrange=True)
        fig.add_annotation(text="No candlestick data available",
                           xref="paper", yref="paper", x=0.5, y=0.5, showarrow=False)
        fig.update_layout(
            xaxis=dict(visible=False),
            yaxis=dict(visible=False),
            margin=dict(l=20, r=20, t=20, b=20),
        )
        return fig

    # Build candlestick chart
    fig = go.Figure(go.Candlestick(
        x=df.index,
        open=df['open'],
        high=df['high'],
        low=df['low'],
        close=df['close'],
        name="BTC/USDT",
        hoverinfo='skip',
    ))

    # Styling consistent with existing charts (no range slider, tight margins, analysis-friendly)
    fig.update_layout(
        dragmode='pan',
        xaxis_rangeslider_visible=False,
        margin=dict(l=10, r=70, t=10, b=10),
        xaxis=dict(showgrid=True, gridcolor="#2a2a2a", showspikes=True, spikemode="across", spikesnap="cursor"),
        yaxis=dict(
            side="right",
            showgrid=True,
            gridcolor="#2a2a2a",
            fixedrange=False,
            showspikes=True,
            spikemode="across",
            showline=True,
            linewidth=1,
            linecolor="#444",
            ticks="outside",
        ),
        hovermode=False,
        uirevision="btc-1h",
    )

    return fig
