import base64
import io
import os
import re
import uuid
from datetime import datetime, timedelta

import dash
import numpy as np
import pandas as pd
import plotly.graph_objects as go
from dash import callback_context, State
from dash import Dash, html, dcc, Input, Output, callback_context, ALL
# from dash_extensions.enrich import DashProxy, Output, Input, State, MATCH, ALL, set_props, Trigger, callback_context, html, dcc

import dash_bootstrap_components as dbc
from dash_bootstrap_templates import load_figure_template

app = Dash(__name__)
app.title = "Candles + Lines (Dash)"


# -------- Demo OHLC generator (replaced when user uploads CSV) --------
def demo_ohlc(n=200, start=None, freq="H", seed=7):
    rng = np.random.default_rng(seed)
    if start is None:
        start = datetime.utcnow() - timedelta(hours=n)
    times = pd.date_range(start=start, periods=n, freq=freq)
    price = 100.0
    rows = []
    for t in times:
        o = price
        h = o + rng.uniform(0.5, 3.5)
        l = o - rng.uniform(0.5, 3.5)
        c = l + rng.uniform(0, (h - l))
        price = c
        rows.append((t, o, h, l, c))
    df = pd.DataFrame(rows, columns=["time", "open", "high", "low", "close"])
    return df


# -------- Helpers --------
def figure_from(df: pd.DataFrame, lines):
    fig = go.Figure(
        data=[
            go.Candlestick(
                x=df["time"],
                open=df["open"], high=df["high"], low=df["low"], close=df["close"],
                name="OHLC",
            )
        ]
    )
    fig.update_layout(
        template="plotly_dark",
        margin=dict(l=10, r=10, t=40, b=20),
        xaxis=dict(title="", rangeslider=dict(visible=False)),
        yaxis=dict(title="Price"),
        dragmode="pan",
        hovermode="x",
        showlegend=False,
    )
    # Add horizontal shapes for stored lines (span full width)
    shapes = []
    annotations = []
    for ln in lines:
        price = float(ln["price"])
        shapes.append(dict(
            type="line", xref="paper", x0=0, x1=1, yref="y", y0=price, y1=price,
            line=dict(width=2),
        ))
        annotations.append(dict(
            x=1.0, xref="paper", y=price, yref="y",
            text=f"{price:,.8f}".rstrip("0").rstrip("."),
            xanchor="left", showarrow=False, font=dict(size=11),
            bgcolor="rgba(15,20,30,0.6)", bordercolor="rgba(255,255,255,0.25)", borderwidth=1, borderpad=3
        ))
    fig.update_layout(shapes=shapes, annotations=annotations)
    return fig


def parse_csv(contents: str) -> pd.DataFrame:
    content_type, content_string = contents.split(",")
    decoded = base64.b64decode(content_string)
    buf = io.StringIO(decoded.decode("utf-8"))
    df = pd.read_csv(buf)
    # Accept columns: time,open,high,low,close  (time ISO, seconds, or ms)
    # Normalize 'time' to pandas datetime
    t = df.iloc[:, 0].astype(str)

    def to_dt(v):
        v = v.strip()
        if v.isdigit():
            if len(v) == 13:  # ms
                return pd.to_datetime(int(v), unit="ms", utc=True)
            elif len(v) == 10:  # seconds
                return pd.to_datetime(int(v), unit="s", utc=True)
        # ISO or anything pandas can parse
        return pd.to_datetime(v, utc=True)

    dt = t.map(to_dt)
    out = pd.DataFrame({
        "time": dt,
        "open": pd.to_numeric(df.iloc[:, 1], errors="coerce"),
        "high": pd.to_numeric(df.iloc[:, 2], errors="coerce"),
        "low": pd.to_numeric(df.iloc[:, 3], errors="coerce"),
        "close": pd.to_numeric(df.iloc[:, 4], errors="coerce"),
    }).dropna()
    return out


# -------- Layout --------
app.layout = html.Div(
    style={"display": "grid", "gridTemplateColumns": "1fr 320px", "height": "100vh", "background": "#0e1116", "color": "#c9d1d9",
           "fontFamily": "system-ui, -apple-system, Segoe UI, Roboto"},
    children=[
        html.Div(
            style={"height": "100%", "padding": "8px"},
            children=[
                dcc.Graph(
                    id="chart",
                    style={"height": "100%"},
                    figure=figure_from(demo_ohlc(), []),
                    config={
                        "displayModeBar": True,
                        "doubleClick": "reset",
                        "scrollZoom": True,
                        "modeBarButtonsToAdd": ["drawline", "eraseshape", "hovercompare"],
                    },
                )
            ]
        ),
        html.Aside(
            style={"borderLeft": "1px solid #222", "padding": "10px"},
            children=[
                html.Div(style={"display": "flex", "gap": "8px", "flexWrap": "wrap", "marginBottom": "10px"}, children=[
                    html.Button("🧹 Clear Lines", id="clear-lines", n_clicks=0,
                                style={"background": "#222833", "border": "1px solid #2b3340", "color": "#c9d1d9", "padding": "8px 10px", "borderRadius": "8px",
                                       "cursor": "pointer"}),
                    dcc.Upload(
                        id="upload",
                        children=html.Div(["📁 ", html.Span("Load CSV (time,open,high,low,close)", style={"textDecoration": "underline", "cursor": "pointer"})]),
                        style={"border": "1px dashed #2b3340", "padding": "8px 10px", "borderRadius": "8px"},
                        multiple=False,
                    ),
                ]),
                html.Div([
                    html.Div("Click the chart to add a horizontal line at the click price.",
                             style={"fontSize": "13px", "color": "#8b949e", "marginBottom": "8px"}),
                    html.Div("Lines", style={"fontWeight": "bold", "marginTop": "6px"}),
                    html.Div(id="lines-list", style={"marginTop": "6px", "display": "grid", "rowGap": "6px"}),
                ], style={"fontSize": "14px"}),
                dcc.Store(id="ohlc-store", data=demo_ohlc().to_dict("records")),
                dcc.Store(id="lines-store", data=[]),
                dcc.Store(id="last-click", data=None),
            ]
        )
    ]
)


# -------- Callbacks --------

# Update OHLC from upload
@app.callback(
    Output("ohlc-store", "data"),
    Input("upload", "contents"),
    prevent_initial_call=True
)
def on_upload(contents):
    try:
        df = parse_csv(contents)
        if df.empty:
            return dash.no_update
        return df.to_dict("records")
    except Exception:
        return dash.no_update


# Render chart whenever data or lines change
@app.callback(
    Output("chart", "figure"),
    Input("ohlc-store", "data"),
    Input("lines-store", "data"),
)
def render_chart(ohlc_records, lines):
    df = pd.DataFrame(ohlc_records)
    return figure_from(df, lines or [])


# Capture clicks -> add horizontal line at clicked price
@app.callback(
    Output("lines-store", "data"),
    Input("chart", "clickData"),
    Input("clear-lines", "n_clicks"),
    Input({"type": "remove-line", "index": ALL}, "n_clicks"),
    State("lines-store", "data"),
    prevent_initial_call=True
)
def modify_lines(clickData, clear_clicks, remove_clicks, lines):
    lines = list(lines or [])
    ctx = callback_context

    if not ctx.triggered:
        return dash.no_update

    trig = ctx.triggered[0]["prop_id"]

    # Clear all
    if trig.startswith("clear-lines"):
        return []

    # Remove specific (find which index fired)
    if "remove-line" in trig:
        # Figure out which button was clicked
        # ctx.inputs has the list; find the one with value and >0 and just-fired
        # Simpler: parse index out of prop_id
        try:
            # prop_id looks like {"type":"remove-line","index":"..."}\.n_clicks
            prefix = '{"type":"remove-line","index":"'
            idx = trig[len(prefix):].split('"')[0]
        except Exception:
            return dash.no_update
        return [ln for ln in lines if ln["id"] != idx]

    # Add on chart click
    if trig.startswith("chart.clickData") and clickData:
        try:
            price = float(clickData["points"][0]["y"])
        except Exception:
            return dash.no_update
        new_line = {"id": str(uuid.uuid4()), "price": price}
        return lines + [new_line]

    return dash.no_update


# Render the sidebar list of lines with delete buttons
@app.callback(
    Output("lines-list", "children"),
    Input("lines-store", "data"),
)
def render_lines_list(lines):
    lines = list(lines or [])
    if not lines:
        return html.Div("No lines yet. Click the chart to add one.", style={"color": "#8b949e"})
    rows = []
    for ln in lines:
        price_str = f"{float(ln['price']):,.8f}".rstrip("0").rstrip(".")
        rows.append(
            html.Div(
                style={"display": "flex", "justifyContent": "space-between", "alignItems": "center", "gap": "10px", "border": "1px solid #2b3340",
                       "padding": "6px 8px", "borderRadius": "8px"},
                children=[
                    html.Div([
                        html.Span("L", style={"opacity": 0.6}),
                        html.Span(ln["id"][:4], style={"opacity": 0.6, "marginRight": "8px"}),
                        html.Span(price_str, style={"background": "#0b1d33", "border": "1px solid #14304f", "borderRadius": "999px", "padding": "2px 8px"})
                    ]),
                    html.Button("🗑️", id={"type": "remove-line", "index": ln["id"]}, n_clicks=0,
                                style={"background": "#222833", "border": "1px solid #2b3340", "color": "#c9d1d9", "padding": "4px 8px", "borderRadius": "6px",
                                       "cursor": "pointer"})
                ]
            )
        )
    return rows


if __name__ == "__main__":
    app.run(debug=False)

# from dash import Dash, dcc, html, Input, Output, State
#
# # 1) Define your defaults
# default_options = ["Alpha", "Bravo", "Charlie"]
#
# app = Dash(__name__)
#
# # 2) Layout: a Store + a Dropdown + a Div to show selections
# app.layout = html.Div([
#     # keep your defaults here
#     dcc.Store(id="default-options", data=default_options),
#
#     dcc.Dropdown(
#         id="dropdown",
#         options=[{"label": opt, "value": opt} for opt in default_options],
#         value=[],
#         multi=True,
#         placeholder="Search or select…",
#     ),
#
#     html.Div(id="selected-output", style={"marginTop": "1rem"})
# ])
#
#
# # 3) Callback to update the dropdown’s options
# @app.callback(
#     Output("dropdown", "options"),
#     Input("dropdown", "search_value"),
#     Input("dropdown", "value"),
#     State("default-options", "data"),
#     prevent_initial_call=True
# )
# def show_current_search_value_as_custom_dropdown_option(search_value, current_values, defaults):
#     # start with the defaults
#     new_options = [{"label": d, "value": d} for d in defaults]
#
#     # if the user’s search text isn’t already in defaults, add it
#     if search_value and search_value not in defaults:
#         new_options.append({"label": search_value, "value": search_value})
#
#     # also re-add any selected values that aren’t in defaults,
#     # so they don’t get dropped when you clear the search
#     if current_values:
#         for val in current_values:
#             if val not in defaults:
#                 new_options.append({"label": val, "value": val})
#
#     return new_options


# 4) (Optional) Show what’s selected
# @app.callback(
#     Output("selected-output", "children"),
#     Input("dropdown", "value")
# )
# def display_selected(values):
#     return f"Selected values: {values}"

#
# if __name__ == "__main__":
#     app.run(debug=False)

# import tweepy
# from solana.rpc.api import Client
#
# # Twitter API credentials (you need to fill these in)
# API_KEY = '*************************'
# API_SECRET_KEY = 'q0lKCq55nG49XAmc0H7jcn30kbuVtbs0TSagkMGFTSpItjIi3T'
# ACCESS_TOKEN = '1892653748201385984-YgncIUbPj0g9yfWeAWPJOqyII3xR6F'
# ACCESS_TOKEN_SECRET = 'YX0pjBORPfZOvTw7pHfxo2w1Rpba0gw3NEWm2czv9ZSud'
#
# # Initialize Twitter API client
# def get_twitter_api():
#     auth = tweepy.OAuth1UserHandler(API_KEY, API_SECRET_KEY, ACCESS_TOKEN, ACCESS_TOKEN_SECRET)
#     return tweepy.API(auth)
#
# # Fetch trending topics from Twitter for a given location (WOEID)
# def get_trending_topics(api, woeid=23424848):
#     try:
#         trends_result = api.available_trends()
#         # trends_result = api.
#         trending_topics = [trend['name'] for trend in trends_result[0]['trends']]
#         print("Trending topics:", trending_topics)
#         return trending_topics
#     except Exception as e:
#         print("Error fetching trending topics:", e)
#         return []
#
# # Solana RPC client setup (mainnet-beta endpoint)
# solana_client = Client("https://api.mainnet-beta.solana.com")
#
# # Example searching of tokens (pseudocode, replace with real implementation)
# def search_solana_for_memecoins(trending_topics):
#     # This part is pseudocode and should be replaced with your logic to query Solana
#     try:
#         # Assume you have a function to list recent tokens or active token metadata
#         recent_tokens = []  # List to be filled with token data
#
#         print("Searching Solana for matches...")
#         for token in recent_tokens:
#             token_name = token.get('name', '').lower()
#             for trend in trending_topics:
#                 if trend.lower() in token_name:
#                     print(f"Found memecoin {token_name} matching trend {trend}")
#     except Exception as e:
#         print("Error searching Solana:", e)
#
# def main():
#     # Get the Twitter API client
#     twitter_api = get_twitter_api()
#
#     # Fetch trending topics
#     trending_topics = get_trending_topics(twitter_api)
#
#     # Search for trending memecoins on Solana
#     # search_solana_for_memecoins(trending_topics)
#
# if __name__ == '__main__':
#     main()
#
#
# import time
#
# from services.bybit import Bybit

# import requests
# from requests_oauthlib import OAuth1
#
# # Your API keys and tokens
# API_KEY = '*************************'
# API_SECRET_KEY = 'q0lKCq55nG49XAmc0H7jcn30kbuVtbs0TSagkMGFTSpItjIi3T'
# ACCESS_TOKEN = '1892653748201385984-YgncIUbPj0g9yfWeAWPJOqyII3xR6F'
# ACCESS_TOKEN_SECRET = 'YX0pjBORPfZOvTw7pHfxo2w1Rpba0gw3NEWm2czv9ZSud'
#
# # Use OAuth1 from requests_oauthlib to sign the requests
# auth = OAuth1(API_KEY, API_SECRET_KEY, ACCESS_TOKEN, ACCESS_TOKEN_SECRET)
#
# # Example of making a request
# def call_trends_endpoint():
#     url = "https://api.x.com/2/usage/personalized_trends"
#     try:
#         response = requests.get(url, auth=auth)
#         print(response.status_code)
#         print(response.json())
#     except Exception as e:
#         print(f"Error: {e}")
#
# def main():
#     call_trends_endpoint()
#
# if __name__ == '__main__':
#     main()

############################
# import requests
#
# proxies = {
#     "http": "socks5h://**************:1080",
#     "https": "socks5h://**************:1080"
# }
#
# try:
#     response = requests.get("https://ifconfig.me", proxies=proxies, timeout=10)
#     vpn_ip = response.text.strip()
#     print(f"🛡️ Traffic is exiting via VPN: {vpn_ip}")
# except Exception as e:
#     print("❌ VPN proxy test failed:", str(e))
######################################

# 3 days ago in milliseconds
# start_time = int(time.time() - 3 * 86400) * 1000
# print(start_time)
# price = Bybit.getOrders(startTime=start_time)
#
# print(price)

# import base64
# import requests
#
# # Your credentials
# API_KEY = '*************************'
# API_SECRET_KEY = 'q0lKCq55nG49XAmc0H7jcn30kbuVtbs0TSagkMGFTSpItjIi3T'
#
# def get_bearer_token(api_key, api_secret_key):
#     bearer_token_credential = f"{api_key}:{api_secret_key}"
#     base64_bearer_token = base64.b64encode(bearer_token_credential.encode('ascii')).decode('ascii')
#
#     url = "https://api.twitter.com/oauth2/token"
#     headers = {
#         "Authorization": f"Basic {base64_bearer_token}",
#         "Content-Type": "application/x-www-form-urlencoded;charset=UTF-8"
#     }
#     body = {
#         "grant_type": "client_credentials"
#     }
#
#     response = requests.post(url, headers=headers, data=body)
#
#     if response.status_code == 200:
#         bearer_token = response.json()['access_token']
#         print(f"Bearer Token: {bearer_token}")
#         return bearer_token
#     else:
#         print(f"Failed to get bearer token: {response.status_code}")
#         print(response.text)
#         return None
#
# def call_trends_endpoint(bearer_token):
#     url = "https://api.x.com/2/tweets"
#     headers = {
#         "Authorization": f"Bearer {bearer_token}"
#     }
#     response = requests.get(url, headers=headers)
#     print(response.status_code)
#     print(response.text)
#
# def main():
#     bearer_token = get_bearer_token(API_KEY, API_SECRET_KEY)
#     if bearer_token:
#         call_trends_endpoint(bearer_token)
#
# if __name__ == '__main__':
#     main()
